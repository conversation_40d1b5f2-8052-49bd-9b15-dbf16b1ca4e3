/* Modern Design System 2025 */

/* Современные переменные для дизайн-системы */
:root {
  /* Основная палитра */
  --primary-blue: #0066ff;
  --primary-blue-hover: #0052cc;
  --primary-blue-light: #e6f0ff;
  --primary-green: #00cc88;
  --primary-green-hover: #00aa66;
  
  /* Нейтральные цвета */
  --neutral-100: #ffffff;
  --neutral-200: #f7f9fc;
  --neutral-300: #edf2f7;
  --neutral-400: #e2e8f0;
  --neutral-500: #cbd5e0;
  --neutral-600: #a0aec0;
  --neutral-700: #718096;
  --neutral-800: #4a5568;
  --neutral-900: #2d3748;
  
  /* Темная тема */
  --dark-bg-primary: #111827;
  --dark-bg-secondary: #1f2937;
  --dark-bg-tertiary: #374151;
  --dark-text-primary: #f9fafb;
  --dark-text-secondary: #e5e7eb;
  
  /* Эффекты */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.12);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  --glass-blur: 12px;
  
  /* Анимации */
  --transition-fast: 0.15s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  
  /* Радиусы */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
  --radius-full: 9999px;
}

/* Современные компоненты */

/* Кнопки 2025 */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
  font-weight: 500;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary-modern {
  background: linear-gradient(135deg, var(--primary-blue), #3385ff);
  color: white;
  box-shadow: 0 4px 14px rgba(0, 102, 255, 0.25);
}

.btn-primary-modern:hover {
  box-shadow: 0 6px 20px rgba(0, 102, 255, 0.35);
  transform: translateY(-2px);
}

.btn-secondary-modern {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  color: inherit;
}

.btn-secondary-modern:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Карточки 2025 */
.card-modern {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius-lg);
  transition: all var(--transition-medium);
  overflow: hidden;
}

.dark .card-modern {
  background: rgba(31, 41, 55, 0.7);
  border-color: rgba(255, 255, 255, 0.05);
}

.card-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Градиентные заголовки */
.gradient-heading {
  background: linear-gradient(135deg, #ffffff, #a5c8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.dark .gradient-heading {
  background: linear-gradient(135deg, #ffffff, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Современные эффекты */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(var(--glass-blur));
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.dark .glass-effect {
  background: rgba(17, 24, 39, 0.6);
  border-color: rgba(255, 255, 255, 0.05);
}

/* Современные анимации */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.float-animation {
  animation: float 5s ease-in-out infinite;
}

@keyframes pulse-subtle {
  0% { opacity: 0.9; }
  50% { opacity: 1; }
  100% { opacity: 0.9; }
}

.pulse-animation {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* Современные декоративные элементы */
.decorative-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  z-index: 0;
  mix-blend-mode: overlay;
}

.decorative-blur {
  position: absolute;
  filter: blur(100px);
  opacity: 0.5;
  z-index: 0;
  border-radius: 50%;
}

/* Современные текстовые стили */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Современные навигационные элементы */
.nav-link-modern {
  position: relative;
  transition: all var(--transition-medium);
}

.nav-link-modern::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: currentColor;
  transition: width var(--transition-medium);
}

.nav-link-modern:hover::after,
.nav-link-modern.active::after {
  width: 100%;
}

/* Современные формы */
.input-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(var(--glass-blur));
  transition: all var(--transition-medium);
}

.input-modern:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.15);
}

.dark .input-modern {
  background: rgba(17, 24, 39, 0.6);
  border-color: rgba(255, 255, 255, 0.05);
}

.dark .input-modern:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.2);
}

/* Современные скроллбары */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 224, 0.5);
  border-radius: var(--radius-full);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(113, 128, 150, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 174, 192, 0.8);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 174, 192, 0.5);
}