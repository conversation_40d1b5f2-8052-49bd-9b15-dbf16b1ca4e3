/* Исправление для заголовка на мобильных устройствах */
@media (max-width: 768px) {
  .hero-section h1 {
    margin-top: 4rem;
  }
}

/* Улучшенные стили для навигации */
.nav-link-active {
  position: relative;
}

.nav-link-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 1px;
}

/* Улучшенные эффекты для кнопок */
.btn-hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.5);
}

/* Улучшенные стили для карточек */
.card-hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}