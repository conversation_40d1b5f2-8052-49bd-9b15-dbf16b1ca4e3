// Импортируем только используемые иконки
import {
  Award,
  Check,
  CheckCircle,
  Clock,
  DollarSign,
  ExternalLink,
  FlaskConical,
  Gift,
  Globe,
  Headphones,
  HeartPulse,
  LifeBuoy,
  Lightbulb,
  Mail,
  MapPin,
  Menu,
  Microscope,
  Phone,
  PieChart,
  Quote,
  Send,
  ShieldCheck,
  Scale,
  ShoppingCart,
  Sparkles,
  Shield,
  Star,
  Tag,
  TrendingUp,
  Users,
  UserPlus,
  User,
  // Whatsapp (будем использовать Phone)
  X,
  Zap,
  ArrowRight,
  Sun,
  Moon,
} from "lucide-react";

export const Icons = {
  Check,
  ExternalLink,
  HeartPulse,
  ShieldCheck,
  Scale,
  Sparkles,
  Star,
  PieChart,
  Clock,
  Globe,
  Headphones,
  Lightbulb,
  Mail,
  MapPin,
  Menu,
  Microscope,
  Phone,
  Quote,
  Send,
  Shield,
  ShoppingCart,
  Tag,
  TrendingUp,
  Users,
  UserPlus,
  User,
  Whatsapp: Phone, // Используем Phone как замену Whatsapp, который не доступен в lucide-react
  X,
  Zap,
  ArrowRight,
  Award,
  CheckCircle,
  DollarSign,
  FlaskConical,
  Gift,
  LifeBuoy,
  Sun,
  Moon,
};

export type IconName = keyof typeof Icons;
