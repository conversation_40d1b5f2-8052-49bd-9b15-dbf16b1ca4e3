// WebGL Fluid Simulation Core
// Adapted from https://github.com/PavelDoGreat/WebGL-Fluid-Simulation

import * as shaders from "./fluidShaders";

export interface FluidConfig {
  SIM_RESOLUTION: number;
  DYE_RESOLUTION: number;
  DENSITY_DISSIPATION: number;
  VELOCITY_DISSIPATION: number;
  PRESSURE: number;
  PRESSURE_ITERATIONS: number;
  CURL: number;
  SPLAT_RADIUS: number;
  SPLAT_FORCE: number;
  SHADING: boolean;
  COLORFUL: boolean;
  COLOR_UPDATE_SPEED: number;
  PAUSED: boolean;
  BACK_COLOR: { r: number; g: number; b: number };
  TRANSPARENT: boolean;
  BLOOM: boolean;
  BLOOM_ITERATIONS: number;
  BLOOM_RESOLUTION: number;
  BLOOM_INTENSITY: number;
  BLOOM_THRESHOLD: number;
  BLOOM_SOFT_KNEE: number;
  SUNRAYS: boolean;
  SUNRAYS_RESOLUTION: number;
  SUNRAYS_WEIGHT: number;
}

interface WebGLExtensions {
  formatRGBA: any;
  formatRG: any;
  formatR: any;
  halfFloatTexType: number;
  supportLinearFiltering: boolean;
}

interface Pointer {
  id: number;
  texcoordX: number;
  texcoordY: number;
  prevTexcoordX: number;
  prevTexcoordY: number;
  deltaX: number;
  deltaY: number;
  down: boolean;
  moved: boolean;
  color: [number, number, number];
}

class Material {
  vertexShader: WebGLShader;
  fragmentShaderSource: string;
  programs: { [key: number]: WebGLProgram } = {};
  activeProgram: WebGLProgram | null = null;
  uniforms: { [key: string]: WebGLUniformLocation | null } = {};
  gl: WebGLRenderingContext | WebGL2RenderingContext;

  constructor(
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    vertexShader: WebGLShader,
    fragmentShaderSource: string
  ) {
    this.gl = gl;
    this.vertexShader = vertexShader;
    this.fragmentShaderSource = fragmentShaderSource;
  }

  setKeywords(keywords: string[]) {
    let hash = 0;
    for (let i = 0; i < keywords.length; i++) {
      const keyword = keywords[i];
      if (keyword) {
        hash += this.hashCode(keyword);
      }
    }

    let program = this.programs[hash];
    if (program == null) {
      let fragmentShader = this.compileShader(this.gl.FRAGMENT_SHADER, this.fragmentShaderSource, keywords);
      program = this.createProgram(this.vertexShader, fragmentShader);
      this.programs[hash] = program;
    }

    if (program == this.activeProgram) return;

    this.uniforms = this.getUniforms(program);
    this.activeProgram = program;
  }

  bind() {
    this.gl.useProgram(this.activeProgram);
  }

  private hashCode(s: string): number {
    if (s.length == 0) return 0;
    let hash = 0;
    for (let i = 0; i < s.length; i++) {
      hash = (hash << 5) - hash + s.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  }

  private compileShader(type: number, source: string, keywords?: string[]): WebGLShader {
    source = this.addKeywords(source, keywords);

    const shader = this.gl.createShader(type)!;
    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error("Shader compilation error:", this.gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private addKeywords(source: string, keywords?: string[]): string {
    if (keywords == null) return source;
    let keywordsString = "";
    keywords.forEach((keyword) => {
      keywordsString += "#define " + keyword + "\n";
    });
    return keywordsString + source;
  }

  private createProgram(vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram {
    let program = this.gl.createProgram()!;
    this.gl.attachShader(program, vertexShader);
    this.gl.attachShader(program, fragmentShader);
    this.gl.linkProgram(program);

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      console.error("Program linking error:", this.gl.getProgramInfoLog(program));
    }

    return program;
  }

  private getUniforms(program: WebGLProgram): { [key: string]: WebGLUniformLocation | null } {
    let uniforms: { [key: string]: WebGLUniformLocation | null } = {};
    let uniformCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
      let uniformName = this.gl.getActiveUniform(program, i)!.name;
      uniforms[uniformName] = this.gl.getUniformLocation(program, uniformName);
    }
    return uniforms;
  }
}

class Program {
  uniforms: { [key: string]: WebGLUniformLocation | null } = {};
  program: WebGLProgram;
  gl: WebGLRenderingContext | WebGL2RenderingContext;

  constructor(
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    vertexShader: WebGLShader,
    fragmentShader: WebGLShader
  ) {
    this.gl = gl;
    this.program = this.createProgram(vertexShader, fragmentShader);
    this.uniforms = this.getUniforms(this.program);
  }

  bind() {
    this.gl.useProgram(this.program);
  }

  private createProgram(vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram {
    let program = this.gl.createProgram()!;
    this.gl.attachShader(program, vertexShader);
    this.gl.attachShader(program, fragmentShader);
    this.gl.linkProgram(program);

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      console.error("Program linking error:", this.gl.getProgramInfoLog(program));
    }

    return program;
  }

  private getUniforms(program: WebGLProgram): { [key: string]: WebGLUniformLocation | null } {
    let uniforms: { [key: string]: WebGLUniformLocation | null } = {};
    let uniformCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
      let uniformName = this.gl.getActiveUniform(program, i)!.name;
      uniforms[uniformName] = this.gl.getUniformLocation(program, uniformName);
    }
    return uniforms;
  }
}

export class FluidSimulation {
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private ext: WebGLExtensions;
  private config: FluidConfig;
  private canvas: HTMLCanvasElement;

  private pointers: Pointer[] = [];
  private splatStack: number[] = [];

  private lastUpdateTime = Date.now();
  private colorUpdateTimer = 0.0;

  // Programs and materials will be initialized later
  private programs: { [key: string]: Program } = {};
  private displayMaterial: Material | null = null;

  // Framebuffers will be initialized later
  private dye: any;
  private velocity: any;
  private divergence: any;
  private curl: any;
  private pressure: any;

  private animationId: number | null = null;

  constructor(
    canvas: HTMLCanvasElement,
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    ext: WebGLExtensions,
    config: FluidConfig
  ) {
    this.canvas = canvas;
    this.gl = gl;
    this.ext = ext;
    this.config = config;

    this.initPointers();
    this.initShaders();
    this.initFramebuffers();
    this.setupEventListeners();

    // Start the simulation
    this.start();
  }

  private initPointers() {
    this.pointers.push({
      id: -1,
      texcoordX: 0,
      texcoordY: 0,
      prevTexcoordX: 0,
      prevTexcoordY: 0,
      deltaX: 0,
      deltaY: 0,
      down: false,
      moved: false,
      color: [30, 0, 300],
    });
  }

  private initShaders() {
    // Compile base shaders
    const baseVertexShader = this.compileShader(this.gl.VERTEX_SHADER, shaders.baseVertexShader);
    const blurVertexShader = this.compileShader(this.gl.VERTEX_SHADER, shaders.blurVertexShader);

    // Compile fragment shaders
    const blurShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.blurShader);
    const copyShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.copyShader);
    const clearShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.clearShader);
    const colorShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.colorShader);
    const splatShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.splatShader);
    const advectionShader = this.compileShader(
      this.gl.FRAGMENT_SHADER,
      shaders.advectionShader,
      this.ext.supportLinearFiltering ? undefined : ["MANUAL_FILTERING"]
    );
    const divergenceShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.divergenceShader);
    const curlShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.curlShader);
    const vorticityShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.vorticityShader);
    const pressureShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.pressureShader);
    const gradientSubtractShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.gradientSubtractShader);

    // Create programs
    this.programs.blur = new Program(this.gl, blurVertexShader, blurShader);
    this.programs.copy = new Program(this.gl, baseVertexShader, copyShader);
    this.programs.clear = new Program(this.gl, baseVertexShader, clearShader);
    this.programs.color = new Program(this.gl, baseVertexShader, colorShader);
    this.programs.splat = new Program(this.gl, baseVertexShader, splatShader);
    this.programs.advection = new Program(this.gl, baseVertexShader, advectionShader);
    this.programs.divergence = new Program(this.gl, baseVertexShader, divergenceShader);
    this.programs.curl = new Program(this.gl, baseVertexShader, curlShader);
    this.programs.vorticity = new Program(this.gl, baseVertexShader, vorticityShader);
    this.programs.pressure = new Program(this.gl, baseVertexShader, pressureShader);
    this.programs.gradientSubtract = new Program(this.gl, baseVertexShader, gradientSubtractShader);

    // Create display material
    this.displayMaterial = new Material(this.gl, baseVertexShader, shaders.displayShaderSource);
    this.updateKeywords();
  }

  private compileShader(type: number, source: string, keywords?: string[]): WebGLShader {
    if (keywords) {
      let keywordsString = "";
      keywords.forEach((keyword) => {
        keywordsString += "#define " + keyword + "\n";
      });
      source = keywordsString + source;
    }

    const shader = this.gl.createShader(type)!;
    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error("Shader compilation error:", this.gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private updateKeywords() {
    if (!this.displayMaterial) return;

    let displayKeywords = [];
    if (this.config.SHADING) displayKeywords.push("SHADING");
    if (this.config.BLOOM) displayKeywords.push("BLOOM");
    if (this.config.SUNRAYS) displayKeywords.push("SUNRAYS");
    this.displayMaterial.setKeywords(displayKeywords);
  }

  private initFramebuffers() {
    const simRes = this.getResolution(this.config.SIM_RESOLUTION);
    const dyeRes = this.getResolution(this.config.DYE_RESOLUTION);

    const texType = this.ext.halfFloatTexType;
    const rgba = this.ext.formatRGBA;
    const rg = this.ext.formatRG;
    const r = this.ext.formatR;
    const filtering = this.ext.supportLinearFiltering ? this.gl.LINEAR : this.gl.NEAREST;

    this.gl.disable(this.gl.BLEND);

    if (this.dye == null) {
      this.dye = this.createDoubleFBO(
        dyeRes.width,
        dyeRes.height,
        rgba.internalFormat,
        rgba.format,
        texType,
        filtering
      );
    } else {
      this.dye = this.resizeDoubleFBO(
        this.dye,
        dyeRes.width,
        dyeRes.height,
        rgba.internalFormat,
        rgba.format,
        texType,
        filtering
      );
    }

    if (this.velocity == null) {
      this.velocity = this.createDoubleFBO(
        simRes.width,
        simRes.height,
        rg.internalFormat,
        rg.format,
        texType,
        filtering
      );
    } else {
      this.velocity = this.resizeDoubleFBO(
        this.velocity,
        simRes.width,
        simRes.height,
        rg.internalFormat,
        rg.format,
        texType,
        filtering
      );
    }

    this.divergence = this.createFBO(simRes.width, simRes.height, r.internalFormat, r.format, texType, this.gl.NEAREST);
    this.curl = this.createFBO(simRes.width, simRes.height, r.internalFormat, r.format, texType, this.gl.NEAREST);
    this.pressure = this.createDoubleFBO(
      simRes.width,
      simRes.height,
      r.internalFormat,
      r.format,
      texType,
      this.gl.NEAREST
    );
  }

  private setupEventListeners() {
    // Mouse events
    this.canvas.addEventListener("mousedown", this.handleMouseDown);
    this.canvas.addEventListener("mousemove", this.handleMouseMove);
    window.addEventListener("mouseup", this.handleMouseUp);

    // Touch events
    this.canvas.addEventListener("touchstart", this.handleTouchStart);
    this.canvas.addEventListener("touchmove", this.handleTouchMove);
    window.addEventListener("touchend", this.handleTouchEnd);

    // Keyboard events
    window.addEventListener("keydown", this.handleKeyDown);
  }

  private start() {
    this.update();
  }

  private update = () => {
    const dt = this.calcDeltaTime();
    if (this.resizeCanvas()) {
      this.initFramebuffers();
    }
    this.updateColors(dt);
    this.applyInputs();
    if (!this.config.PAUSED) {
      this.step(dt);
    }
    this.render(null);
    this.animationId = requestAnimationFrame(this.update);
  };

  private calcDeltaTime(): number {
    let now = Date.now();
    let dt = (now - this.lastUpdateTime) / 1000;
    dt = Math.min(dt, 0.016666);
    this.lastUpdateTime = now;
    return dt;
  }

  private resizeCanvas(): boolean {
    const pixelRatio = window.devicePixelRatio || 1;
    const width = Math.floor(this.canvas.clientWidth * pixelRatio);
    const height = Math.floor(this.canvas.clientHeight * pixelRatio);

    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
      return true;
    }
    return false;
  }

  private updateColors(dt: number) {
    if (!this.config.COLORFUL) return;

    this.colorUpdateTimer += dt * this.config.COLOR_UPDATE_SPEED;
    if (this.colorUpdateTimer >= 1) {
      this.colorUpdateTimer = this.wrap(this.colorUpdateTimer, 0, 1);
      this.pointers.forEach((p) => {
        p.color = this.generateColor();
      });
    }
  }

  private applyInputs() {
    if (this.splatStack.length > 0) {
      this.multipleSplats(this.splatStack.pop()!);
    }

    this.pointers.forEach((p) => {
      if (p.moved) {
        p.moved = false;
        this.splatPointer(p);
      }
    });
  }

  private step(dt: number) {
    this.gl.disable(this.gl.BLEND);

    // Curl step
    if (this.programs.curl) {
      this.programs.curl.bind();
      const curlUniforms = this.programs.curl.uniforms;
      if (curlUniforms.texelSize) {
        this.gl.uniform2f(curlUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }
      if (curlUniforms.uVelocity) {
        this.gl.uniform1i(curlUniforms.uVelocity, this.velocity.read.attach(0));
      }
      this.blit(this.curl);
    }

    // Vorticity step
    if (this.programs.vorticity) {
      this.programs.vorticity.bind();
      const vorticityUniforms = this.programs.vorticity.uniforms;
      if (vorticityUniforms.texelSize) {
        this.gl.uniform2f(vorticityUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }
      if (vorticityUniforms.uVelocity) {
        this.gl.uniform1i(vorticityUniforms.uVelocity, this.velocity.read.attach(0));
      }
      if (vorticityUniforms.uCurl) {
        this.gl.uniform1i(vorticityUniforms.uCurl, this.curl.attach(1));
      }
      if (vorticityUniforms.curl) {
        this.gl.uniform1f(vorticityUniforms.curl, this.config.CURL);
      }
      if (vorticityUniforms.dt) {
        this.gl.uniform1f(vorticityUniforms.dt, dt);
      }
      this.blit(this.velocity.write);
      this.velocity.swap();
    }

    // Divergence step
    if (this.programs.divergence) {
      this.programs.divergence.bind();
      const divergenceUniforms = this.programs.divergence.uniforms;
      if (divergenceUniforms.texelSize) {
        this.gl.uniform2f(divergenceUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }
      if (divergenceUniforms.uVelocity) {
        this.gl.uniform1i(divergenceUniforms.uVelocity, this.velocity.read.attach(0));
      }
      this.blit(this.divergence);
    }

    // Clear pressure
    if (this.programs.clear) {
      this.programs.clear.bind();
      const clearUniforms = this.programs.clear.uniforms;
      if (clearUniforms.uTexture) {
        this.gl.uniform1i(clearUniforms.uTexture, this.pressure.read.attach(0));
      }
      if (clearUniforms.value) {
        this.gl.uniform1f(clearUniforms.value, this.config.PRESSURE);
      }
      this.blit(this.pressure.write);
      this.pressure.swap();
    }

    // Pressure iterations
    if (this.programs.pressure) {
      this.programs.pressure.bind();
      const pressureUniforms = this.programs.pressure.uniforms;
      if (pressureUniforms.texelSize) {
        this.gl.uniform2f(pressureUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }
      if (pressureUniforms.uDivergence) {
        this.gl.uniform1i(pressureUniforms.uDivergence, this.divergence.attach(0));
      }
      for (let i = 0; i < this.config.PRESSURE_ITERATIONS; i++) {
        if (pressureUniforms.uPressure) {
          this.gl.uniform1i(pressureUniforms.uPressure, this.pressure.read.attach(1));
        }
        this.blit(this.pressure.write);
        this.pressure.swap();
      }
    }

    // Gradient subtract
    if (this.programs.gradientSubtract) {
      this.programs.gradientSubtract.bind();
      const gradientUniforms = this.programs.gradientSubtract.uniforms;
      if (gradientUniforms.texelSize) {
        this.gl.uniform2f(gradientUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }
      if (gradientUniforms.uPressure) {
        this.gl.uniform1i(gradientUniforms.uPressure, this.pressure.read.attach(0));
      }
      if (gradientUniforms.uVelocity) {
        this.gl.uniform1i(gradientUniforms.uVelocity, this.velocity.read.attach(1));
      }
      this.blit(this.velocity.write);
      this.velocity.swap();
    }

    // Advection
    if (this.programs.advection) {
      this.programs.advection.bind();
      const advectionUniforms = this.programs.advection.uniforms;

      if (advectionUniforms.texelSize) {
        this.gl.uniform2f(advectionUniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }

      if (!this.ext.supportLinearFiltering && advectionUniforms.dyeTexelSize) {
        this.gl.uniform2f(advectionUniforms.dyeTexelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
      }

      let velocityId = this.velocity.read.attach(0);
      if (advectionUniforms.uVelocity) {
        this.gl.uniform1i(advectionUniforms.uVelocity, velocityId);
      }
      if (advectionUniforms.uSource) {
        this.gl.uniform1i(advectionUniforms.uSource, velocityId);
      }
      if (advectionUniforms.dt) {
        this.gl.uniform1f(advectionUniforms.dt, dt);
      }
      if (advectionUniforms.dissipation) {
        this.gl.uniform1f(advectionUniforms.dissipation, this.config.VELOCITY_DISSIPATION);
      }
      this.blit(this.velocity.write);
      this.velocity.swap();

      if (!this.ext.supportLinearFiltering && advectionUniforms.dyeTexelSize) {
        this.gl.uniform2f(advectionUniforms.dyeTexelSize, this.dye.texelSizeX, this.dye.texelSizeY);
      }
      if (advectionUniforms.uVelocity) {
        this.gl.uniform1i(advectionUniforms.uVelocity, this.velocity.read.attach(0));
      }
      if (advectionUniforms.uSource) {
        this.gl.uniform1i(advectionUniforms.uSource, this.dye.read.attach(1));
      }
      if (advectionUniforms.dissipation) {
        this.gl.uniform1f(advectionUniforms.dissipation, this.config.DENSITY_DISSIPATION);
      }
      this.blit(this.dye.write);
      this.dye.swap();
    }
  }

  private render(target: any) {
    if (target == null || !this.config.TRANSPARENT) {
      this.gl.blendFunc(this.gl.ONE, this.gl.ONE_MINUS_SRC_ALPHA);
      this.gl.enable(this.gl.BLEND);
    } else {
      this.gl.disable(this.gl.BLEND);
    }

    if (!this.config.TRANSPARENT) {
      this.drawColor(target, this.normalizeColor(this.config.BACK_COLOR));
    }
    this.drawDisplay(target);
  }

  // Add missing methods
  private getResolution(resolution: number): { width: number; height: number } {
    let aspectRatio = this.gl.drawingBufferWidth / this.gl.drawingBufferHeight;
    if (aspectRatio < 1) aspectRatio = 1.0 / aspectRatio;

    let min = Math.round(resolution);
    let max = Math.round(resolution * aspectRatio);

    if (this.gl.drawingBufferWidth > this.gl.drawingBufferHeight) {
      return { width: max, height: min };
    } else {
      return { width: min, height: max };
    }
  }

  private createFBO(w: number, h: number, internalFormat: number, format: number, type: number, param: number): any {
    const texture = this.gl.createTexture()!;
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, param);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, param);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, internalFormat, w, h, 0, format, type, null);

    const fbo = this.gl.createFramebuffer()!;
    this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, fbo);
    this.gl.framebufferTexture2D(this.gl.FRAMEBUFFER, this.gl.COLOR_ATTACHMENT0, this.gl.TEXTURE_2D, texture, 0);
    this.gl.viewport(0, 0, w, h);
    this.gl.clear(this.gl.COLOR_BUFFER_BIT);

    let texelSizeX = 1.0 / w;
    let texelSizeY = 1.0 / h;

    return {
      texture,
      fbo,
      width: w,
      height: h,
      texelSizeX,
      texelSizeY,
      attach: (id: number) => {
        this.gl.activeTexture(this.gl.TEXTURE0 + id);
        this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
        return id;
      },
    };
  }

  private createDoubleFBO(
    w: number,
    h: number,
    internalFormat: number,
    format: number,
    type: number,
    param: number
  ): any {
    let fbo1 = this.createFBO(w, h, internalFormat, format, type, param);
    let fbo2 = this.createFBO(w, h, internalFormat, format, type, param);

    return {
      width: w,
      height: h,
      texelSizeX: fbo1.texelSizeX,
      texelSizeY: fbo1.texelSizeY,
      read: fbo1,
      write: fbo2,
      swap() {
        let temp = fbo1;
        fbo1 = fbo2;
        fbo2 = temp;
        this.read = fbo1;
        this.write = fbo2;
      },
    };
  }

  private resizeDoubleFBO(
    target: any,
    w: number,
    h: number,
    internalFormat: number,
    format: number,
    type: number,
    param: number
  ): any {
    if (target.width == w && target.height == h) return target;
    target.read = this.createFBO(w, h, internalFormat, format, type, param);
    target.write = this.createFBO(w, h, internalFormat, format, type, param);
    target.width = w;
    target.height = h;
    target.texelSizeX = 1.0 / w;
    target.texelSizeY = 1.0 / h;
    return target;
  }

  private blit(target: any) {
    if (target == null) {
      this.gl.viewport(0, 0, this.gl.drawingBufferWidth, this.gl.drawingBufferHeight);
      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
    } else {
      this.gl.viewport(0, 0, target.width, target.height);
      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, target.fbo);
    }
    this.gl.drawArrays(this.gl.TRIANGLES, 0, 6);
  }

  private wrap(value: number, min: number, max: number): number {
    const range = max - min;
    if (range == 0) return min;
    return ((value - min) % range) + min;
  }

  private generateColor(): [number, number, number] {
    let c = this.HSVtoRGB(Math.random(), 1.0, 1.0);
    c.r *= 0.15;
    c.g *= 0.15;
    c.b *= 0.15;
    return [c.r, c.g, c.b];
  }

  private HSVtoRGB(h: number, s: number, v: number): { r: number; g: number; b: number } {
    let r: number, g: number, b: number;
    let i = Math.floor(h * 6);
    let f = h * 6 - i;
    let p = v * (1 - s);
    let q = v * (1 - f * s);
    let t = v * (1 - (1 - f) * s);
    switch (i % 6) {
      case 0:
        (r = v), (g = t), (b = p);
        break;
      case 1:
        (r = q), (g = v), (b = p);
        break;
      case 2:
        (r = p), (g = v), (b = t);
        break;
      case 3:
        (r = p), (g = q), (b = v);
        break;
      case 4:
        (r = t), (g = p), (b = v);
        break;
      case 5:
        (r = v), (g = p), (b = q);
        break;
      default:
        (r = 0), (g = 0), (b = 0);
    }
    return { r, g, b };
  }

  private multipleSplats(amount: number) {
    for (let i = 0; i < amount; i++) {
      const color = this.generateColor();
      color[0] *= 10.0;
      color[1] *= 10.0;
      color[2] *= 10.0;
      const x = Math.random();
      const y = Math.random();
      const dx = 1000 * (Math.random() - 0.5);
      const dy = 1000 * (Math.random() - 0.5);
      this.splat(x, y, dx, dy, color);
    }
  }

  private splat(x: number, y: number, dx: number, dy: number, color: [number, number, number]) {
    if (!this.programs.splat) return;

    this.programs.splat.bind();

    const uniforms = this.programs.splat.uniforms;
    if (uniforms.uTarget) {
      this.gl.uniform1i(uniforms.uTarget, this.velocity.read.attach(0));
    }
    if (uniforms.aspectRatio) {
      this.gl.uniform1f(uniforms.aspectRatio, this.canvas.width / this.canvas.height);
    }
    if (uniforms.point) {
      this.gl.uniform2f(uniforms.point, x, y);
    }
    if (uniforms.color) {
      this.gl.uniform3f(uniforms.color, dx, dy, 0.0);
    }
    if (uniforms.radius) {
      this.gl.uniform1f(uniforms.radius, this.correctRadius(this.config.SPLAT_RADIUS / 100.0));
    }
    this.blit(this.velocity.write);
    this.velocity.swap();

    if (uniforms.uTarget) {
      this.gl.uniform1i(uniforms.uTarget, this.dye.read.attach(0));
    }
    if (uniforms.color) {
      this.gl.uniform3f(uniforms.color, color[0], color[1], color[2]);
    }
    this.blit(this.dye.write);
    this.dye.swap();
  }

  private correctRadius(radius: number): number {
    let aspectRatio = this.canvas.width / this.canvas.height;
    if (aspectRatio > 1) radius *= aspectRatio;
    return radius;
  }

  private splatPointer(pointer: Pointer) {
    let dx = pointer.deltaX * this.config.SPLAT_FORCE;
    let dy = pointer.deltaY * this.config.SPLAT_FORCE;
    this.splat(pointer.texcoordX, pointer.texcoordY, dx, dy, pointer.color);
  }

  // Event handlers
  private handleMouseDown = (e: MouseEvent) => {
    let pointer = this.pointers[0];
    if (!pointer) return;

    let rect = this.canvas.getBoundingClientRect();
    pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    pointer.prevTexcoordX = pointer.texcoordX;
    pointer.prevTexcoordY = pointer.texcoordY;
    pointer.down = true;
    pointer.moved = false;
    pointer.color = this.generateColor();
  };

  private handleMouseMove = (e: MouseEvent) => {
    let pointer = this.pointers[0];
    if (!pointer || !pointer.down) return;

    let rect = this.canvas.getBoundingClientRect();
    pointer.prevTexcoordX = pointer.texcoordX;
    pointer.prevTexcoordY = pointer.texcoordY;
    pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    pointer.deltaX = pointer.texcoordX - pointer.prevTexcoordX;
    pointer.deltaY = pointer.texcoordY - pointer.prevTexcoordY;
    pointer.moved = Math.abs(pointer.deltaX) > 0 || Math.abs(pointer.deltaY) > 0;
  };

  private handleMouseUp = () => {
    const pointer = this.pointers[0];
    if (pointer) {
      pointer.down = false;
    }
  };

  private handleTouchStart = (e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      this.handleMouseDown({
        clientX: touch.clientX,
        clientY: touch.clientY,
      } as MouseEvent);
    }
  };

  private handleTouchMove = (e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      this.handleMouseMove({
        clientX: touch.clientX,
        clientY: touch.clientY,
      } as MouseEvent);
    }
  };

  private handleTouchEnd = (e: TouchEvent) => {
    e.preventDefault();
    this.handleMouseUp();
  };

  private handleKeyDown = (e: KeyboardEvent) => {
    if (e.code === "KeyP") {
      this.config.PAUSED = !this.config.PAUSED;
    }
    if (e.key === " ") {
      this.splatStack.push(parseInt((Math.random() * 20).toString()) + 5);
    }
  };

  // Rendering methods
  private drawColor(target: any, color: { r: number; g: number; b: number }) {
    if (!this.programs.color) return;
    this.programs.color.bind();
    const colorUniform = this.programs.color.uniforms.color;
    if (colorUniform) {
      this.gl.uniform4f(colorUniform, color.r, color.g, color.b, 1);
    }
    this.blit(target);
  }

  private drawDisplay(target: any) {
    if (!this.displayMaterial) return;
    this.displayMaterial.bind();
    const texelSizeUniform = this.displayMaterial.uniforms.texelSize;
    const textureUniform = this.displayMaterial.uniforms.uTexture;
    if (texelSizeUniform) {
      this.gl.uniform2f(texelSizeUniform, 1.0 / this.canvas.width, 1.0 / this.canvas.height);
    }
    if (textureUniform) {
      this.gl.uniform1i(textureUniform, this.dye.read.attach(0));
    }
    this.blit(target);
  }

  private normalizeColor(input: { r: number; g: number; b: number }): { r: number; g: number; b: number } {
    return {
      r: input.r / 255,
      g: input.g / 255,
      b: input.b / 255,
    };
  }

  public destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  public updateConfig(newConfig: Partial<FluidConfig>) {
    this.config = { ...this.config, ...newConfig };
    this.updateKeywords();
  }
}
