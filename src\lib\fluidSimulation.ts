// WebGL Fluid Simulation Core
// Adapted from https://github.com/PavelDoGreat/WebGL-Fluid-Simulation

import * as shaders from "./fluidShaders";

export interface FluidConfig {
  SIM_RESOLUTION: number;
  DYE_RESOLUTION: number;
  DENSITY_DISSIPATION: number;
  VELOCITY_DISSIPATION: number;
  PRESSURE: number;
  PRESSURE_ITERATIONS: number;
  CURL: number;
  SPLAT_RADIUS: number;
  SPLAT_FORCE: number;
  SHADING: boolean;
  COLORFUL: boolean;
  COLOR_UPDATE_SPEED: number;
  PAUSED: boolean;
  BACK_COLOR: { r: number; g: number; b: number };
  TRANSPARENT: boolean;
  BLOOM: boolean;
  BLOOM_ITERATIONS: number;
  BLOOM_RESOLUTION: number;
  BLOOM_INTENSITY: number;
  BLOOM_THRESHOLD: number;
  BLOOM_SOFT_KNEE: number;
  SUNRAYS: boolean;
  SUNRAYS_RESOLUTION: number;
  SUNRAYS_WEIGHT: number;
}

interface WebGLExtensions {
  formatRGBA: any;
  formatRG: any;
  formatR: any;
  halfFloatTexType: number;
  supportLinearFiltering: boolean;
}

interface Pointer {
  id: number;
  texcoordX: number;
  texcoordY: number;
  prevTexcoordX: number;
  prevTexcoordY: number;
  deltaX: number;
  deltaY: number;
  down: boolean;
  moved: boolean;
  color: [number, number, number];
}

class Material {
  vertexShader: WebGLShader;
  fragmentShaderSource: string;
  programs: { [key: number]: WebGLProgram } = {};
  activeProgram: WebGLProgram | null = null;
  uniforms: { [key: string]: WebGLUniformLocation | null } = {};
  gl: WebGLRenderingContext | WebGL2RenderingContext;

  constructor(
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    vertexShader: WebGLShader,
    fragmentShaderSource: string
  ) {
    this.gl = gl;
    this.vertexShader = vertexShader;
    this.fragmentShaderSource = fragmentShaderSource;
  }

  setKeywords(keywords: string[]) {
    let hash = 0;
    for (let i = 0; i < keywords.length; i++) {
      hash += this.hashCode(keywords[i]);
    }

    let program = this.programs[hash];
    if (program == null) {
      let fragmentShader = this.compileShader(this.gl.FRAGMENT_SHADER, this.fragmentShaderSource, keywords);
      program = this.createProgram(this.vertexShader, fragmentShader);
      this.programs[hash] = program;
    }

    if (program == this.activeProgram) return;

    this.uniforms = this.getUniforms(program);
    this.activeProgram = program;
  }

  bind() {
    this.gl.useProgram(this.activeProgram);
  }

  private hashCode(s: string): number {
    if (s.length == 0) return 0;
    let hash = 0;
    for (let i = 0; i < s.length; i++) {
      hash = (hash << 5) - hash + s.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  }

  private compileShader(type: number, source: string, keywords?: string[]): WebGLShader {
    source = this.addKeywords(source, keywords);

    const shader = this.gl.createShader(type)!;
    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error("Shader compilation error:", this.gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private addKeywords(source: string, keywords?: string[]): string {
    if (keywords == null) return source;
    let keywordsString = "";
    keywords.forEach((keyword) => {
      keywordsString += "#define " + keyword + "\n";
    });
    return keywordsString + source;
  }

  private createProgram(vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram {
    let program = this.gl.createProgram()!;
    this.gl.attachShader(program, vertexShader);
    this.gl.attachShader(program, fragmentShader);
    this.gl.linkProgram(program);

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      console.error("Program linking error:", this.gl.getProgramInfoLog(program));
    }

    return program;
  }

  private getUniforms(program: WebGLProgram): { [key: string]: WebGLUniformLocation | null } {
    let uniforms: { [key: string]: WebGLUniformLocation | null } = {};
    let uniformCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
      let uniformName = this.gl.getActiveUniform(program, i)!.name;
      uniforms[uniformName] = this.gl.getUniformLocation(program, uniformName);
    }
    return uniforms;
  }
}

class Program {
  uniforms: { [key: string]: WebGLUniformLocation | null } = {};
  program: WebGLProgram;
  gl: WebGLRenderingContext | WebGL2RenderingContext;

  constructor(
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    vertexShader: WebGLShader,
    fragmentShader: WebGLShader
  ) {
    this.gl = gl;
    this.program = this.createProgram(vertexShader, fragmentShader);
    this.uniforms = this.getUniforms(this.program);
  }

  bind() {
    this.gl.useProgram(this.program);
  }

  private createProgram(vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram {
    let program = this.gl.createProgram()!;
    this.gl.attachShader(program, vertexShader);
    this.gl.attachShader(program, fragmentShader);
    this.gl.linkProgram(program);

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      console.error("Program linking error:", this.gl.getProgramInfoLog(program));
    }

    return program;
  }

  private getUniforms(program: WebGLProgram): { [key: string]: WebGLUniformLocation | null } {
    let uniforms: { [key: string]: WebGLUniformLocation | null } = {};
    let uniformCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
      let uniformName = this.gl.getActiveUniform(program, i)!.name;
      uniforms[uniformName] = this.gl.getUniformLocation(program, uniformName);
    }
    return uniforms;
  }
}

export class FluidSimulation {
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private ext: WebGLExtensions;
  private config: FluidConfig;
  private canvas: HTMLCanvasElement;

  private pointers: Pointer[] = [];
  private splatStack: number[] = [];

  private lastUpdateTime = Date.now();
  private colorUpdateTimer = 0.0;

  // Programs and materials will be initialized later
  private programs: { [key: string]: Program } = {};
  private displayMaterial: Material | null = null;

  // Framebuffers will be initialized later
  private dye: any;
  private velocity: any;
  private divergence: any;
  private curl: any;
  private pressure: any;

  private animationId: number | null = null;

  constructor(
    canvas: HTMLCanvasElement,
    gl: WebGLRenderingContext | WebGL2RenderingContext,
    ext: WebGLExtensions,
    config: FluidConfig
  ) {
    this.canvas = canvas;
    this.gl = gl;
    this.ext = ext;
    this.config = config;

    this.initPointers();
    this.initShaders();
    this.initFramebuffers();
    this.setupEventListeners();

    // Start the simulation
    this.start();
  }

  private initPointers() {
    this.pointers.push({
      id: -1,
      texcoordX: 0,
      texcoordY: 0,
      prevTexcoordX: 0,
      prevTexcoordY: 0,
      deltaX: 0,
      deltaY: 0,
      down: false,
      moved: false,
      color: [30, 0, 300],
    });
  }

  private initShaders() {
    // Compile base shaders
    const baseVertexShader = this.compileShader(this.gl.VERTEX_SHADER, shaders.baseVertexShader);
    const blurVertexShader = this.compileShader(this.gl.VERTEX_SHADER, shaders.blurVertexShader);

    // Compile fragment shaders
    const blurShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.blurShader);
    const copyShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.copyShader);
    const clearShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.clearShader);
    const colorShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.colorShader);
    const splatShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.splatShader);
    const advectionShader = this.compileShader(
      this.gl.FRAGMENT_SHADER,
      shaders.advectionShader,
      this.ext.supportLinearFiltering ? undefined : ["MANUAL_FILTERING"]
    );
    const divergenceShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.divergenceShader);
    const curlShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.curlShader);
    const vorticityShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.vorticityShader);
    const pressureShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.pressureShader);
    const gradientSubtractShader = this.compileShader(this.gl.FRAGMENT_SHADER, shaders.gradientSubtractShader);

    // Create programs
    this.programs.blur = new Program(this.gl, blurVertexShader, blurShader);
    this.programs.copy = new Program(this.gl, baseVertexShader, copyShader);
    this.programs.clear = new Program(this.gl, baseVertexShader, clearShader);
    this.programs.color = new Program(this.gl, baseVertexShader, colorShader);
    this.programs.splat = new Program(this.gl, baseVertexShader, splatShader);
    this.programs.advection = new Program(this.gl, baseVertexShader, advectionShader);
    this.programs.divergence = new Program(this.gl, baseVertexShader, divergenceShader);
    this.programs.curl = new Program(this.gl, baseVertexShader, curlShader);
    this.programs.vorticity = new Program(this.gl, baseVertexShader, vorticityShader);
    this.programs.pressure = new Program(this.gl, baseVertexShader, pressureShader);
    this.programs.gradientSubtract = new Program(this.gl, baseVertexShader, gradientSubtractShader);

    // Create display material
    this.displayMaterial = new Material(this.gl, baseVertexShader, shaders.displayShaderSource);
    this.updateKeywords();
  }

  private compileShader(type: number, source: string, keywords?: string[]): WebGLShader {
    if (keywords) {
      let keywordsString = "";
      keywords.forEach((keyword) => {
        keywordsString += "#define " + keyword + "\n";
      });
      source = keywordsString + source;
    }

    const shader = this.gl.createShader(type)!;
    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error("Shader compilation error:", this.gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private updateKeywords() {
    if (!this.displayMaterial) return;

    let displayKeywords = [];
    if (this.config.SHADING) displayKeywords.push("SHADING");
    if (this.config.BLOOM) displayKeywords.push("BLOOM");
    if (this.config.SUNRAYS) displayKeywords.push("SUNRAYS");
    this.displayMaterial.setKeywords(displayKeywords);
  }

  private initFramebuffers() {
    const simRes = this.getResolution(this.config.SIM_RESOLUTION);
    const dyeRes = this.getResolution(this.config.DYE_RESOLUTION);

    const texType = this.ext.halfFloatTexType;
    const rgba = this.ext.formatRGBA;
    const rg = this.ext.formatRG;
    const r = this.ext.formatR;
    const filtering = this.ext.supportLinearFiltering ? this.gl.LINEAR : this.gl.NEAREST;

    this.gl.disable(this.gl.BLEND);

    if (this.dye == null) {
      this.dye = this.createDoubleFBO(
        dyeRes.width,
        dyeRes.height,
        rgba.internalFormat,
        rgba.format,
        texType,
        filtering
      );
    } else {
      this.dye = this.resizeDoubleFBO(
        this.dye,
        dyeRes.width,
        dyeRes.height,
        rgba.internalFormat,
        rgba.format,
        texType,
        filtering
      );
    }

    if (this.velocity == null) {
      this.velocity = this.createDoubleFBO(
        simRes.width,
        simRes.height,
        rg.internalFormat,
        rg.format,
        texType,
        filtering
      );
    } else {
      this.velocity = this.resizeDoubleFBO(
        this.velocity,
        simRes.width,
        simRes.height,
        rg.internalFormat,
        rg.format,
        texType,
        filtering
      );
    }

    this.divergence = this.createFBO(simRes.width, simRes.height, r.internalFormat, r.format, texType, this.gl.NEAREST);
    this.curl = this.createFBO(simRes.width, simRes.height, r.internalFormat, r.format, texType, this.gl.NEAREST);
    this.pressure = this.createDoubleFBO(
      simRes.width,
      simRes.height,
      r.internalFormat,
      r.format,
      texType,
      this.gl.NEAREST
    );
  }

  private setupEventListeners() {
    // Mouse events
    this.canvas.addEventListener("mousedown", this.handleMouseDown);
    this.canvas.addEventListener("mousemove", this.handleMouseMove);
    window.addEventListener("mouseup", this.handleMouseUp);

    // Touch events
    this.canvas.addEventListener("touchstart", this.handleTouchStart);
    this.canvas.addEventListener("touchmove", this.handleTouchMove);
    window.addEventListener("touchend", this.handleTouchEnd);

    // Keyboard events
    window.addEventListener("keydown", this.handleKeyDown);
  }

  private start() {
    this.update();
  }

  private update = () => {
    const dt = this.calcDeltaTime();
    if (this.resizeCanvas()) {
      this.initFramebuffers();
    }
    this.updateColors(dt);
    this.applyInputs();
    if (!this.config.PAUSED) {
      this.step(dt);
    }
    this.render(null);
    this.animationId = requestAnimationFrame(this.update);
  };

  private calcDeltaTime(): number {
    let now = Date.now();
    let dt = (now - this.lastUpdateTime) / 1000;
    dt = Math.min(dt, 0.016666);
    this.lastUpdateTime = now;
    return dt;
  }

  private resizeCanvas(): boolean {
    const pixelRatio = window.devicePixelRatio || 1;
    const width = Math.floor(this.canvas.clientWidth * pixelRatio);
    const height = Math.floor(this.canvas.clientHeight * pixelRatio);

    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
      return true;
    }
    return false;
  }

  private updateColors(dt: number) {
    if (!this.config.COLORFUL) return;

    this.colorUpdateTimer += dt * this.config.COLOR_UPDATE_SPEED;
    if (this.colorUpdateTimer >= 1) {
      this.colorUpdateTimer = this.wrap(this.colorUpdateTimer, 0, 1);
      this.pointers.forEach((p) => {
        p.color = this.generateColor();
      });
    }
  }

  private applyInputs() {
    if (this.splatStack.length > 0) {
      this.multipleSplats(this.splatStack.pop()!);
    }

    this.pointers.forEach((p) => {
      if (p.moved) {
        p.moved = false;
        this.splatPointer(p);
      }
    });
  }

  private step(dt: number) {
    this.gl.disable(this.gl.BLEND);

    // Curl step
    this.programs.curl.bind();
    this.gl.uniform2f(this.programs.curl.uniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
    this.gl.uniform1i(this.programs.curl.uniforms.uVelocity, this.velocity.read.attach(0));
    this.blit(this.curl);

    // Vorticity step
    this.programs.vorticity.bind();
    this.gl.uniform2f(this.programs.vorticity.uniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
    this.gl.uniform1i(this.programs.vorticity.uniforms.uVelocity, this.velocity.read.attach(0));
    this.gl.uniform1i(this.programs.vorticity.uniforms.uCurl, this.curl.attach(1));
    this.gl.uniform1f(this.programs.vorticity.uniforms.curl, this.config.CURL);
    this.gl.uniform1f(this.programs.vorticity.uniforms.dt, dt);
    this.blit(this.velocity.write);
    this.velocity.swap();

    // Divergence step
    this.programs.divergence.bind();
    this.gl.uniform2f(this.programs.divergence.uniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
    this.gl.uniform1i(this.programs.divergence.uniforms.uVelocity, this.velocity.read.attach(0));
    this.blit(this.divergence);

    // Clear pressure
    this.programs.clear.bind();
    this.gl.uniform1i(this.programs.clear.uniforms.uTexture, this.pressure.read.attach(0));
    this.gl.uniform1f(this.programs.clear.uniforms.value, this.config.PRESSURE);
    this.blit(this.pressure.write);
    this.pressure.swap();

    // Pressure iterations
    this.programs.pressure.bind();
    this.gl.uniform2f(this.programs.pressure.uniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
    this.gl.uniform1i(this.programs.pressure.uniforms.uDivergence, this.divergence.attach(0));
    for (let i = 0; i < this.config.PRESSURE_ITERATIONS; i++) {
      this.gl.uniform1i(this.programs.pressure.uniforms.uPressure, this.pressure.read.attach(1));
      this.blit(this.pressure.write);
      this.pressure.swap();
    }

    // Gradient subtract
    this.programs.gradientSubtract.bind();
    this.gl.uniform2f(
      this.programs.gradientSubtract.uniforms.texelSize,
      this.velocity.texelSizeX,
      this.velocity.texelSizeY
    );
    this.gl.uniform1i(this.programs.gradientSubtract.uniforms.uPressure, this.pressure.read.attach(0));
    this.gl.uniform1i(this.programs.gradientSubtract.uniforms.uVelocity, this.velocity.read.attach(1));
    this.blit(this.velocity.write);
    this.velocity.swap();

    // Advection
    this.programs.advection.bind();
    this.gl.uniform2f(this.programs.advection.uniforms.texelSize, this.velocity.texelSizeX, this.velocity.texelSizeY);
    if (!this.ext.supportLinearFiltering) {
      this.gl.uniform2f(
        this.programs.advection.uniforms.dyeTexelSize,
        this.velocity.texelSizeX,
        this.velocity.texelSizeY
      );
    }
    let velocityId = this.velocity.read.attach(0);
    this.gl.uniform1i(this.programs.advection.uniforms.uVelocity, velocityId);
    this.gl.uniform1i(this.programs.advection.uniforms.uSource, velocityId);
    this.gl.uniform1f(this.programs.advection.uniforms.dt, dt);
    this.gl.uniform1f(this.programs.advection.uniforms.dissipation, this.config.VELOCITY_DISSIPATION);
    this.blit(this.velocity.write);
    this.velocity.swap();

    if (!this.ext.supportLinearFiltering) {
      this.gl.uniform2f(this.programs.advection.uniforms.dyeTexelSize, this.dye.texelSizeX, this.dye.texelSizeY);
    }
    this.gl.uniform1i(this.programs.advection.uniforms.uVelocity, this.velocity.read.attach(0));
    this.gl.uniform1i(this.programs.advection.uniforms.uSource, this.dye.read.attach(1));
    this.gl.uniform1f(this.programs.advection.uniforms.dissipation, this.config.DENSITY_DISSIPATION);
    this.blit(this.dye.write);
    this.dye.swap();
  }

  private render(target: any) {
    if (target == null || !this.config.TRANSPARENT) {
      this.gl.blendFunc(this.gl.ONE, this.gl.ONE_MINUS_SRC_ALPHA);
      this.gl.enable(this.gl.BLEND);
    } else {
      this.gl.disable(this.gl.BLEND);
    }

    if (!this.config.TRANSPARENT) {
      this.drawColor(target, this.normalizeColor(this.config.BACK_COLOR));
    }
    this.drawDisplay(target);
  }

  public destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  public updateConfig(newConfig: Partial<FluidConfig>) {
    this.config = { ...this.config, ...newConfig };
    this.updateKeywords();
  }
}
