/* Анимации для меню */

/* Эффект "жидкости" для меню */
.liquid-menu-item {
  position: relative;
  overflow: hidden;
}

.liquid-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  transform: scale(0);
  transform-origin: center;
  transition: transform 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: -1;
}

.liquid-menu-item:hover::before {
  transform: scale(1);
}

/* Эффект стекломорфизма */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glassmorphism {
  background: rgba(17, 24, 39, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Микро-взаимодействия для меню */
.micro-interaction {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.micro-interaction:hover {
  transform: translateY(-2px);
}

/* Анимация для мега-меню */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mega-menu-animation {
  animation: fadeInDown 0.3s ease-out forwards;
}

/* Эффект "вязкости" для мобильного меню */
.gooey-effect {
  filter: url('#gooey');
}

/* Анимация для мобильного меню */
@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.mobile-menu-animation {
  animation: scaleIn 0.3s ease-out forwards;
}

/* SVG фильтр для эффекта "вязкости" */
.svg-filters {
  position: absolute;
  width: 0;
  height: 0;
  z-index: -1000;
}