#Temporary
WebGL-Fluid-Simulation-master
Данные для ИИ-Агента/

# Logs

logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
node_modules
dist
dist-ssr
*.local

# Editor directories and files

project_contents.txt
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables

.env
.env.local
.env.development.local
.env.test.local
.env.production
.env.production.local


# Build files

build/
out/
.cache/

# Coverage reports

coverage/

# Misc

.vercel
.netlify