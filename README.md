# 4Life - Персональный сайт дистрибьютора

Персональный сайт дистрибьютора 4Life с информацией о продуктах, партнерстве и контактах.

## Технологии

- React 18
- TypeScript
- Vite
- Framer Motion
- TailwindCSS
- React Router
- React Helmet Async
- React Scroll Parallax

## Структура проекта

- `src/` - исходный код
  - `animations/` - анимации и варианты для Framer Motion
  - `components/` - React компоненты
    - `layout/` - компоненты макета (Header, Footer и т.д.)
    - `ui/` - переиспользуемые UI компоненты
    - `utils/` - вспомогательные компоненты
  - `config/` - конфигурация сайта
  - `context/` - React контексты
  - `hooks/` - пользовательские хуки
  - `lib/` - вспомогательные функции
  - `pages/` - страницы сайта
  - `styles/` - CSS стили
  - `types/` - TypeScript типы
  - `utils/` - утилиты

## Особенности

- Плавная прокрутка с использованием библиотеки Lenis
- Темная и светлая темы с автоматическим определением системных настроек
- Адаптивный дизайн для мобильных и десктопных устройств
- Анимации с использованием Framer Motion
- Оптимизированная загрузка с ленивой загрузкой компонентов

## Лицензия

MIT
