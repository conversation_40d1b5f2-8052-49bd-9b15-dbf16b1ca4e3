import React, { useCallback, useEffect, useRef } from "react";

interface FluidBackgroundProps {
  className?: string;
  children?: React.ReactNode;
  intensity?: number; // 0-1, интенсивность эффекта
  colors?: string[]; // массив цветов для эффекта
  interactive?: boolean; // включить интерактивность
}

// Простая реализация fluid эффекта с использованием CSS и Canvas
interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
  size: number;
}

const FluidBackground: React.FC<FluidBackgroundProps> = ({
  className = "",
  children,
  intensity = 0.5,
  colors = ["#3b82f6", "#8b5cf6", "#06b6d4", "#10b981"],
  interactive = true,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0, isDown: false });

  // Removed unused isMobile function

  // Создание частицы
  const createParticle = useCallback((x: number, y: number, color: string) => {
    return {
      x,
      y,
      vx: (Math.random() - 0.5) * 4,
      vy: (Math.random() - 0.5) * 4,
      life: 1,
      maxLife: 60 + Math.random() * 60,
      color,
      size: 2 + Math.random() * 4,
    };
  }, []);

  // Добавление частиц
  const addParticles = useCallback(
    (x: number, y: number, count: number = 5) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      for (let i = 0; i < count; i++) {
        const color = colors[Math.floor(Math.random() * colors.length)] || "#3b82f6";
        const particle = createParticle(x, y, color);
        particlesRef.current.push(particle);
      }

      // Ограничиваем количество частиц
      if (particlesRef.current.length > 200) {
        particlesRef.current = particlesRef.current.slice(-200);
      }
    },
    [colors, createParticle]
  );

  // Анимация частиц
  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Очистка с эффектом затухания
    ctx.fillStyle = "rgba(0, 0, 0, 0.05)";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Обновление и отрисовка частиц
    particlesRef.current = particlesRef.current.filter((particle) => {
      // Обновление позиции
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Затухание скорости
      particle.vx *= 0.99;
      particle.vy *= 0.99;

      // Уменьшение жизни
      particle.life -= 1 / particle.maxLife;

      // Удаление мертвых частиц
      if (particle.life <= 0) return false;

      // Отрисовка частицы
      const alpha = particle.life * intensity;
      ctx.save();
      ctx.globalAlpha = alpha;
      ctx.fillStyle = particle.color;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size * particle.life, 0, Math.PI * 2);
      ctx.fill();

      // Добавляем свечение
      ctx.shadowColor = particle.color;
      ctx.shadowBlur = 10 * particle.life;
      ctx.fill();
      ctx.restore();

      return true;
    });

    animationRef.current = requestAnimationFrame(animate);
  }, [intensity]);

  // Обработчики событий мыши
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!interactive) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const x = (e.clientX - rect.left) * (canvas.width / rect.width);
      const y = (e.clientY - rect.top) * (canvas.height / rect.height);

      mouseRef.current.x = x;
      mouseRef.current.y = y;

      if (mouseRef.current.isDown) {
        addParticles(x, y, 3);
      }
    },
    [interactive, addParticles]
  );

  const handleMouseDown = useCallback(
    (e: MouseEvent) => {
      if (!interactive) return;
      mouseRef.current.isDown = true;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const x = (e.clientX - rect.left) * (canvas.width / rect.width);
      const y = (e.clientY - rect.top) * (canvas.height / rect.height);

      addParticles(x, y, 8);
    },
    [interactive, addParticles]
  );

  const handleMouseUp = useCallback(() => {
    mouseRef.current.isDown = false;
  }, []);

  // Обработчики touch событий
  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!interactive) return;
      e.preventDefault();

      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const touch = e.touches[0];
      if (!touch) return;

      const x = (touch.clientX - rect.left) * (canvas.width / rect.width);
      const y = (touch.clientY - rect.top) * (canvas.height / rect.height);

      addParticles(x, y, 2);
    },
    [interactive, addParticles]
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Функция ресайза
    const resizeCanvas = () => {
      if (!containerRef.current || !canvas) return;

      const rect = containerRef.current.getBoundingClientRect();
      const pixelRatio = window.devicePixelRatio || 1;

      canvas.width = rect.width * pixelRatio;
      canvas.height = rect.height * pixelRatio;
      canvas.style.width = rect.width + "px";
      canvas.style.height = rect.height + "px";

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.scale(pixelRatio, pixelRatio);
      }
    };

    resizeCanvas();

    // Обработчики событий
    if (interactive) {
      canvas.addEventListener("mousemove", handleMouseMove);
      canvas.addEventListener("mousedown", handleMouseDown);
      window.addEventListener("mouseup", handleMouseUp);
      canvas.addEventListener("touchmove", handleTouchMove, { passive: false });
      canvas.addEventListener("touchstart", handleTouchMove, { passive: false });
    }

    // Обработчик ресайза окна
    window.addEventListener("resize", resizeCanvas);

    // Запуск анимации
    animate();

    // Автоматическое добавление частиц
    const autoParticles = setInterval(() => {
      if (particlesRef.current.length < 50) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        addParticles(x, y, 1);
      }
    }, 2000);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      clearInterval(autoParticles);

      if (interactive) {
        canvas.removeEventListener("mousemove", handleMouseMove);
        canvas.removeEventListener("mousedown", handleMouseDown);
        window.removeEventListener("mouseup", handleMouseUp);
        canvas.removeEventListener("touchmove", handleTouchMove);
        canvas.removeEventListener("touchstart", handleTouchMove);
      }

      window.removeEventListener("resize", resizeCanvas);
    };
  }, [interactive, animate, handleMouseMove, handleMouseDown, handleMouseUp, handleTouchMove, addParticles]);

  return (
    <div ref={containerRef} className={`relative w-full h-full overflow-hidden ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{
          touchAction: "none",
          userSelect: "none",
        }}
      />
      {children && <div className="relative z-10 w-full h-full">{children}</div>}
    </div>
  );
};

export default FluidBackground;
