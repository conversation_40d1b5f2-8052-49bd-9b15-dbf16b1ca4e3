// Полная реализация WebGL Fluid Simulation
// Адаптировано из https://github.com/PavelDoGreat/WebGL-Fluid-Simulation

export interface FluidConfig {
  SIM_RESOLUTION: number;
  DYE_RESOLUTION: number;
  DENSITY_DISSIPATION: number;
  VELOCITY_DISSIPATION: number;
  PRESSURE: number;
  PRESSURE_ITERATIONS: number;
  CURL: number;
  SPLAT_RADIUS: number;
  SPLAT_FORCE: number;
  SHADING: boolean;
  COLORFUL: boolean;
  COLOR_UPDATE_SPEED: number;
  PAUSED: boolean;
  BACK_COLOR: { r: number; g: number; b: number };
  TRANSPARENT: boolean;
  BLOOM: boolean;
  BLOOM_ITERATIONS: number;
  BLOOM_RESOLUTION: number;
  BLOOM_INTENSITY: number;
  BLOOM_THRESHOLD: number;
  BLOOM_SOFT_KNEE: number;
  SUNRAYS: boolean;
  SUNRAYS_RESOLUTION: number;
  SUNRAYS_WEIGHT: number;
}

const defaultConfig: FluidConfig = {
  SIM_RESOLUTION: 128,
  DYE_RESOLUTION: 1024,
  DENSITY_DISSIPATION: 1,
  VELOCITY_DISSIPATION: 0.2,
  PRESSURE: 0.8,
  PRESSURE_ITERATIONS: 20,
  CURL: 30,
  SPLAT_RADIUS: 0.25,
  SPLAT_FORCE: 6000,
  SHADING: true,
  COLORFUL: true,
  COLOR_UPDATE_SPEED: 10,
  PAUSED: false,
  BACK_COLOR: { r: 0, g: 0, b: 0 },
  TRANSPARENT: false,
  BLOOM: true,
  BLOOM_ITERATIONS: 8,
  BLOOM_RESOLUTION: 256,
  BLOOM_INTENSITY: 0.8,
  BLOOM_THRESHOLD: 0.6,
  BLOOM_SOFT_KNEE: 0.7,
  SUNRAYS: true,
  SUNRAYS_RESOLUTION: 196,
  SUNRAYS_WEIGHT: 1.0,
};

// Простые шейдеры для базовой реализации
const vertexShader = `
  precision highp float;
  attribute vec2 aPosition;
  varying vec2 vUv;
  void main () {
    vUv = aPosition * 0.5 + 0.5;
    gl_Position = vec4(aPosition, 0.0, 1.0);
  }
`;

const fragmentShader = `
  precision mediump float;
  varying vec2 vUv;
  uniform sampler2D uTexture;
  uniform vec3 color;
  uniform vec2 point;
  uniform float radius;
  uniform float aspectRatio;
  
  void main () {
    vec2 p = vUv - point.xy;
    p.x *= aspectRatio;
    vec3 splat = exp(-dot(p, p) / radius) * color;
    vec3 base = texture2D(uTexture, vUv).xyz;
    gl_FragColor = vec4(base + splat, 1.0);
  }
`;

const displayShader = `
  precision mediump float;
  varying vec2 vUv;
  uniform sampler2D uTexture;
  
  void main () {
    vec3 c = texture2D(uTexture, vUv).rgb;
    float a = max(c.r, max(c.g, c.b));
    gl_FragColor = vec4(c, a);
  }
`;

interface Pointer {
  id: number;
  texcoordX: number;
  texcoordY: number;
  prevTexcoordX: number;
  prevTexcoordY: number;
  deltaX: number;
  deltaY: number;
  down: boolean;
  moved: boolean;
  color: [number, number, number];
}

export class WebGLFluidSimulation {
  private canvas: HTMLCanvasElement;
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private config: FluidConfig;
  private pointers: Pointer[] = [];
  private splatStack: number[] = [];
  private lastUpdateTime = Date.now();
  private colorUpdateTimer = 0.0;
  private animationId: number | null = null;

  // WebGL объекты
  private program: WebGLProgram | null = null;
  private displayProgram: WebGLProgram | null = null;
  private quadBuffer: WebGLBuffer | null = null;
  private framebuffer: WebGLFramebuffer | null = null;
  private texture: WebGLTexture | null = null;

  constructor(canvas: HTMLCanvasElement, userConfig: Partial<FluidConfig> = {}) {
    this.canvas = canvas;
    this.config = { ...defaultConfig, ...userConfig };
    
    // Инициализация WebGL
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    if (!gl) {
      throw new Error('WebGL not supported');
    }
    this.gl = gl;

    this.initWebGL();
    this.initPointers();
    this.setupEventListeners();
    this.start();
  }

  private initWebGL() {
    const gl = this.gl;

    // Создание шейдеров
    this.program = this.createProgram(vertexShader, fragmentShader);
    this.displayProgram = this.createProgram(vertexShader, displayShader);

    // Создание буфера для квада
    this.quadBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
      -1, -1, 1, -1, -1, 1, 1, 1
    ]), gl.STATIC_DRAW);

    // Создание текстуры и фреймбуфера
    this.texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, this.texture);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

    this.framebuffer = gl.createFramebuffer();
    
    this.resizeCanvas();
  }

  private createProgram(vertexSource: string, fragmentSource: string): WebGLProgram {
    const gl = this.gl;
    
    const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);
    
    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program linking error:', gl.getProgramInfoLog(program));
    }

    return program;
  }

  private createShader(type: number, source: string): WebGLShader {
    const gl = this.gl;
    const shader = gl.createShader(type)!;
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private initPointers() {
    this.pointers.push({
      id: -1,
      texcoordX: 0,
      texcoordY: 0,
      prevTexcoordX: 0,
      prevTexcoordY: 0,
      deltaX: 0,
      deltaY: 0,
      down: false,
      moved: false,
      color: [Math.random() + 0.2, Math.random() + 0.2, Math.random() + 0.2]
    });
  }

  private setupEventListeners() {
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    window.addEventListener('mouseup', this.handleMouseUp.bind(this));
    
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    window.addEventListener('touchend', this.handleTouchEnd.bind(this));
  }

  private handleMouseDown(e: MouseEvent) {
    const pointer = this.pointers[0];
    const rect = this.canvas.getBoundingClientRect();
    pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    pointer.prevTexcoordX = pointer.texcoordX;
    pointer.prevTexcoordY = pointer.texcoordY;
    pointer.down = true;
    pointer.moved = false;
    pointer.color = [Math.random() + 0.2, Math.random() + 0.2, Math.random() + 0.2];
  }

  private handleMouseMove(e: MouseEvent) {
    const pointer = this.pointers[0];
    if (!pointer.down) return;
    
    const rect = this.canvas.getBoundingClientRect();
    pointer.prevTexcoordX = pointer.texcoordX;
    pointer.prevTexcoordY = pointer.texcoordY;
    pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    pointer.deltaX = pointer.texcoordX - pointer.prevTexcoordX;
    pointer.deltaY = pointer.texcoordY - pointer.prevTexcoordY;
    pointer.moved = Math.abs(pointer.deltaX) > 0 || Math.abs(pointer.deltaY) > 0;
  }

  private handleMouseUp() {
    this.pointers[0].down = false;
  }

  private handleTouchStart(e: TouchEvent) {
    e.preventDefault();
    const touch = e.touches[0];
    this.handleMouseDown({
      clientX: touch.clientX,
      clientY: touch.clientY
    } as MouseEvent);
  }

  private handleTouchMove(e: TouchEvent) {
    e.preventDefault();
    const touch = e.touches[0];
    this.handleMouseMove({
      clientX: touch.clientX,
      clientY: touch.clientY
    } as MouseEvent);
  }

  private handleTouchEnd(e: TouchEvent) {
    e.preventDefault();
    this.handleMouseUp();
  }

  private resizeCanvas(): boolean {
    const pixelRatio = window.devicePixelRatio || 1;
    const width = Math.floor(this.canvas.clientWidth * pixelRatio);
    const height = Math.floor(this.canvas.clientHeight * pixelRatio);
    
    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
      
      const gl = this.gl;
      gl.viewport(0, 0, width, height);
      
      // Обновление текстуры
      gl.bindTexture(gl.TEXTURE_2D, this.texture);
      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);
      
      return true;
    }
    return false;
  }

  private start() {
    this.update();
  }

  private update = () => {
    const dt = this.calcDeltaTime();
    if (this.resizeCanvas()) {
      // Ресайз произошел
    }
    this.updateColors(dt);
    this.applyInputs();
    if (!this.config.PAUSED) {
      this.step(dt);
    }
    this.render();
    this.animationId = requestAnimationFrame(this.update);
  };

  private calcDeltaTime(): number {
    const now = Date.now();
    const dt = (now - this.lastUpdateTime) / 1000;
    this.lastUpdateTime = now;
    return Math.min(dt, 0.016666);
  }

  private updateColors(dt: number) {
    if (!this.config.COLORFUL) return;
    
    this.colorUpdateTimer += dt * this.config.COLOR_UPDATE_SPEED;
    if (this.colorUpdateTimer >= 1) {
      this.colorUpdateTimer = 0;
      this.pointers.forEach(p => {
        p.color = [Math.random() + 0.2, Math.random() + 0.2, Math.random() + 0.2];
      });
    }
  }

  private applyInputs() {
    this.pointers.forEach(pointer => {
      if (pointer.moved) {
        this.splat(pointer.texcoordX, pointer.texcoordY, pointer.deltaX, pointer.deltaY, pointer.color);
        pointer.moved = false;
      }
    });
  }

  private splat(x: number, y: number, dx: number, dy: number, color: [number, number, number]) {
    const gl = this.gl;
    
    gl.bindFramebuffer(gl.FRAMEBUFFER, this.framebuffer);
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, this.texture, 0);
    
    gl.useProgram(this.program);
    
    const aPosition = gl.getAttribLocation(this.program!, 'aPosition');
    gl.enableVertexAttribArray(aPosition);
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);
    
    const uTexture = gl.getUniformLocation(this.program!, 'uTexture');
    const uColor = gl.getUniformLocation(this.program!, 'color');
    const uPoint = gl.getUniformLocation(this.program!, 'point');
    const uRadius = gl.getUniformLocation(this.program!, 'radius');
    const uAspectRatio = gl.getUniformLocation(this.program!, 'aspectRatio');
    
    gl.uniform1i(uTexture, 0);
    gl.uniform3f(uColor, color[0], color[1], color[2]);
    gl.uniform2f(uPoint, x, y);
    gl.uniform1f(uRadius, this.config.SPLAT_RADIUS / 100.0);
    gl.uniform1f(uAspectRatio, this.canvas.width / this.canvas.height);
    
    gl.bindTexture(gl.TEXTURE_2D, this.texture);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  private step(dt: number) {
    // Простая реализация - в полной версии здесь будет симуляция жидкости
  }

  private render() {
    const gl = this.gl;
    
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.clear(gl.COLOR_BUFFER_BIT);
    
    gl.useProgram(this.displayProgram);
    
    const aPosition = gl.getAttribLocation(this.displayProgram!, 'aPosition');
    gl.enableVertexAttribArray(aPosition);
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);
    
    const uTexture = gl.getUniformLocation(this.displayProgram!, 'uTexture');
    gl.uniform1i(uTexture, 0);
    
    gl.bindTexture(gl.TEXTURE_2D, this.texture);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  public destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    // Очистка WebGL ресурсов
    const gl = this.gl;
    if (this.program) gl.deleteProgram(this.program);
    if (this.displayProgram) gl.deleteProgram(this.displayProgram);
    if (this.quadBuffer) gl.deleteBuffer(this.quadBuffer);
    if (this.texture) gl.deleteTexture(this.texture);
    if (this.framebuffer) gl.deleteFramebuffer(this.framebuffer);
  }

  public updateConfig(newConfig: Partial<FluidConfig>) {
    this.config = { ...this.config, ...newConfig };
  }
}
