/*
MIT License
Copyright (c) 2017 <PERSON>
Simplified WebGL Fluid Simulation for React/TypeScript
*/

export interface FluidConfig {
  SIM_RESOLUTION: number;
  DYE_RESOLUTION: number;
  DENSITY_DISSIPATION: number;
  VELOCITY_DISSIPATION: number;
  PRESSURE: number;
  PRESSURE_ITERATIONS: number;
  CURL: number;
  SPLAT_RADIUS: number;
  SPLAT_FORCE: number;
  SHADING: boolean;
  COLORFUL: boolean;
  COLOR_UPDATE_SPEED: number;
  PAUSED: boolean;
  BACK_COLOR: { r: number; g: number; b: number };
  TRANSPARENT: boolean;
  BLOOM: boolean;
  BLOOM_ITERATIONS: number;
  BLOOM_RESOLUTION: number;
  BLOOM_INTENSITY: number;
  BLOOM_THRESHOLD: number;
  BLOOM_SOFT_KNEE: number;
  SUNRAYS: boolean;
  SUNRAYS_RESOLUTION: number;
  SUNRAYS_WEIGHT: number;
}

interface Pointer {
  id: number;
  texcoordX: number;
  texcoordY: number;
  prevTexcoordX: number;
  prevTexcoordY: number;
  deltaX: number;
  deltaY: number;
  down: boolean;
  moved: boolean;
  color: { r: number; g: number; b: number };
}

// Simplified shaders
const vertexShader = `
  precision highp float;
  attribute vec2 aPosition;
  varying vec2 vUv;
  void main () {
    vUv = aPosition * 0.5 + 0.5;
    gl_Position = vec4(aPosition, 0.0, 1.0);
  }
`;

const fragmentShader = `
  precision mediump float;
  varying vec2 vUv;
  uniform sampler2D uTexture;
  uniform vec3 color;
  uniform vec2 point;
  uniform float radius;
  uniform float aspectRatio;
  
  void main () {
    vec2 p = vUv - point.xy;
    p.x *= aspectRatio;
    vec3 splat = exp(-dot(p, p) / radius) * color;
    vec3 base = texture2D(uTexture, vUv).xyz;
    gl_FragColor = vec4(base + splat, 1.0);
  }
`;

const displayShader = `
  precision mediump float;
  varying vec2 vUv;
  uniform sampler2D uTexture;
  
  void main () {
    vec3 c = texture2D(uTexture, vUv).rgb;
    float a = max(c.r, max(c.g, c.b));
    gl_FragColor = vec4(c, a);
  }
`;

export class WebGLFluidSimulation {
  private canvas: HTMLCanvasElement;
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private config: FluidConfig;
  private pointer: Pointer;
  private animationId: number | null = null;
  private lastUpdateTime = Date.now();
  private colorUpdateTimer = 0.0;

  // WebGL objects
  private program: WebGLProgram | null = null;
  private displayProgram: WebGLProgram | null = null;
  private quadBuffer: WebGLBuffer | null = null;
  private framebuffer: WebGLFramebuffer | null = null;
  private texture: WebGLTexture | null = null;

  constructor(canvas: HTMLCanvasElement, userConfig: Partial<FluidConfig> = {}) {
    this.canvas = canvas;

    const defaultConfig: FluidConfig = {
      SIM_RESOLUTION: 128,
      DYE_RESOLUTION: 1024,
      DENSITY_DISSIPATION: 1,
      VELOCITY_DISSIPATION: 0.2,
      PRESSURE: 0.8,
      PRESSURE_ITERATIONS: 20,
      CURL: 30,
      SPLAT_RADIUS: 0.25,
      SPLAT_FORCE: 6000,
      SHADING: true,
      COLORFUL: true,
      COLOR_UPDATE_SPEED: 10,
      PAUSED: false,
      BACK_COLOR: { r: 0, g: 0, b: 0 },
      TRANSPARENT: false,
      BLOOM: true,
      BLOOM_ITERATIONS: 8,
      BLOOM_RESOLUTION: 256,
      BLOOM_INTENSITY: 0.8,
      BLOOM_THRESHOLD: 0.6,
      BLOOM_SOFT_KNEE: 0.7,
      SUNRAYS: true,
      SUNRAYS_RESOLUTION: 196,
      SUNRAYS_WEIGHT: 1.0,
    };

    this.config = { ...defaultConfig, ...userConfig };

    // Initialize pointer
    this.pointer = {
      id: -1,
      texcoordX: 0,
      texcoordY: 0,
      prevTexcoordX: 0,
      prevTexcoordY: 0,
      deltaX: 0,
      deltaY: 0,
      down: false,
      moved: false,
      color: { r: 30, g: 0, b: 300 },
    };

    // Initialize WebGL
    const gl = canvas.getContext("webgl2") || canvas.getContext("webgl");
    if (!gl) {
      throw new Error("WebGL not supported");
    }
    this.gl = gl;

    this.initWebGL();
    this.setupEventListeners();
    this.multipleSplats(5);
    this.start();
  }

  private initWebGL() {
    const gl = this.gl;

    // Create shaders and programs
    this.program = this.createProgram(vertexShader, fragmentShader);
    this.displayProgram = this.createProgram(vertexShader, displayShader);

    // Create quad buffer
    this.quadBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]), gl.STATIC_DRAW);

    // Create textures and framebuffers
    this.texture = this.createTexture();
    this.framebuffer = this.createFramebuffer(this.texture);

    // Initialize canvas size and texture
    this.resizeCanvas();
  }

  private createProgram(vertexSource: string, fragmentSource: string): WebGLProgram {
    const gl = this.gl;

    const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);

    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error("Program linking error:", gl.getProgramInfoLog(program));
    }

    return program;
  }

  private createShader(type: number, source: string): WebGLShader {
    const gl = this.gl;
    const shader = gl.createShader(type)!;
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error("Shader compilation error:", gl.getShaderInfoLog(shader));
    }

    return shader;
  }

  private createTexture(): WebGLTexture {
    const gl = this.gl;
    const texture = gl.createTexture()!;
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    // Initialize with default size
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);
    return texture;
  }

  private createFramebuffer(texture: WebGLTexture): WebGLFramebuffer {
    const gl = this.gl;
    const framebuffer = gl.createFramebuffer()!;
    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);
    return framebuffer;
  }

  private setupEventListeners() {
    this.canvas.addEventListener("mousedown", this.handleMouseDown.bind(this));
    this.canvas.addEventListener("mousemove", this.handleMouseMove.bind(this));
    window.addEventListener("mouseup", this.handleMouseUp.bind(this));

    this.canvas.addEventListener("touchstart", this.handleTouchStart.bind(this));
    this.canvas.addEventListener("touchmove", this.handleTouchMove.bind(this));
    window.addEventListener("touchend", this.handleTouchEnd.bind(this));
  }

  private handleMouseDown(e: MouseEvent) {
    const rect = this.canvas.getBoundingClientRect();
    this.pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    this.pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    this.pointer.prevTexcoordX = this.pointer.texcoordX;
    this.pointer.prevTexcoordY = this.pointer.texcoordY;
    this.pointer.down = true;
    this.pointer.moved = false;
    this.pointer.color = this.generateColor();
  }

  private handleMouseMove(e: MouseEvent) {
    if (!this.pointer.down) return;

    const rect = this.canvas.getBoundingClientRect();
    this.pointer.prevTexcoordX = this.pointer.texcoordX;
    this.pointer.prevTexcoordY = this.pointer.texcoordY;
    this.pointer.texcoordX = (e.clientX - rect.left) / rect.width;
    this.pointer.texcoordY = 1.0 - (e.clientY - rect.top) / rect.height;
    this.pointer.deltaX = this.pointer.texcoordX - this.pointer.prevTexcoordX;
    this.pointer.deltaY = this.pointer.texcoordY - this.pointer.prevTexcoordY;
    this.pointer.moved = Math.abs(this.pointer.deltaX) > 0 || Math.abs(this.pointer.deltaY) > 0;
  }

  private handleMouseUp() {
    this.pointer.down = false;
  }

  private handleTouchStart(e: TouchEvent) {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      this.handleMouseDown({
        clientX: touch.clientX,
        clientY: touch.clientY,
      } as MouseEvent);
    }
  }

  private handleTouchMove(e: TouchEvent) {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      this.handleMouseMove({
        clientX: touch.clientX,
        clientY: touch.clientY,
      } as MouseEvent);
    }
  }

  private handleTouchEnd(e: TouchEvent) {
    e.preventDefault();
    this.handleMouseUp();
  }

  private generateColor(): { r: number; g: number; b: number } {
    const c = this.HSVtoRGB(Math.random(), 1.0, 1.0);
    return {
      r: c.r * 0.15,
      g: c.g * 0.15,
      b: c.b * 0.15,
    };
  }

  private HSVtoRGB(h: number, s: number, v: number): { r: number; g: number; b: number } {
    let r: number, g: number, b: number;
    const i = Math.floor(h * 6);
    const f = h * 6 - i;
    const p = v * (1 - s);
    const q = v * (1 - f * s);
    const t = v * (1 - (1 - f) * s);

    switch (i % 6) {
      case 0:
        (r = v), (g = t), (b = p);
        break;
      case 1:
        (r = q), (g = v), (b = p);
        break;
      case 2:
        (r = p), (g = v), (b = t);
        break;
      case 3:
        (r = p), (g = q), (b = v);
        break;
      case 4:
        (r = t), (g = p), (b = v);
        break;
      case 5:
        (r = v), (g = p), (b = q);
        break;
      default:
        (r = 0), (g = 0), (b = 0);
    }
    return { r, g, b };
  }

  private multipleSplats(amount: number) {
    for (let i = 0; i < amount; i++) {
      const color = this.generateColor();
      color.r *= 10.0;
      color.g *= 10.0;
      color.b *= 10.0;
      const x = Math.random();
      const y = Math.random();
      const dx = 1000 * (Math.random() - 0.5);
      const dy = 1000 * (Math.random() - 0.5);
      this.splat(x, y, dx, dy, color);
    }
  }

  private splat(x: number, y: number, _dx: number, _dy: number, color: { r: number; g: number; b: number }) {
    if (!this.program || !this.texture || !this.framebuffer) return;

    const gl = this.gl;

    // Bind framebuffer for rendering
    gl.bindFramebuffer(gl.FRAMEBUFFER, this.framebuffer);
    gl.viewport(0, 0, this.canvas.width, this.canvas.height);

    // Use splat program
    gl.useProgram(this.program);

    // Set up attributes
    const aPosition = gl.getAttribLocation(this.program, "aPosition");
    gl.enableVertexAttribArray(aPosition);
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);

    // Set uniforms
    const uTexture = gl.getUniformLocation(this.program, "uTexture");
    const uColor = gl.getUniformLocation(this.program, "color");
    const uPoint = gl.getUniformLocation(this.program, "point");
    const uRadius = gl.getUniformLocation(this.program, "radius");
    const uAspectRatio = gl.getUniformLocation(this.program, "aspectRatio");

    gl.uniform1i(uTexture, 0);
    gl.uniform3f(uColor, color.r, color.g, color.b);
    gl.uniform2f(uPoint, x, y);
    gl.uniform1f(uRadius, this.config.SPLAT_RADIUS / 100.0);
    gl.uniform1f(uAspectRatio, this.canvas.width / this.canvas.height);

    // Bind texture
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, this.texture);

    // Draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  private resizeCanvas(): boolean {
    const pixelRatio = window.devicePixelRatio || 1;
    const width = Math.floor(this.canvas.clientWidth * pixelRatio);
    const height = Math.floor(this.canvas.clientHeight * pixelRatio);

    // Ensure minimum size
    const finalWidth = Math.max(width, 1);
    const finalHeight = Math.max(height, 1);

    if (this.canvas.width !== finalWidth || this.canvas.height !== finalHeight) {
      this.canvas.width = finalWidth;
      this.canvas.height = finalHeight;

      // Update texture size
      if (this.texture) {
        const gl = this.gl;
        gl.bindTexture(gl.TEXTURE_2D, this.texture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, finalWidth, finalHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);
      }

      return true;
    }
    return false;
  }

  private start() {
    this.update();
  }

  private update = () => {
    const dt = this.calcDeltaTime();
    if (this.resizeCanvas()) {
      // Canvas was resized
    }
    this.updateColors(dt);
    this.applyInputs();
    if (!this.config.PAUSED) {
      this.step(dt);
    }
    this.render();
    this.animationId = requestAnimationFrame(this.update);
  };

  private calcDeltaTime(): number {
    const now = Date.now();
    const dt = (now - this.lastUpdateTime) / 1000;
    this.lastUpdateTime = now;
    return Math.min(dt, 0.016666);
  }

  private updateColors(dt: number) {
    if (!this.config.COLORFUL) return;

    this.colorUpdateTimer += dt * this.config.COLOR_UPDATE_SPEED;
    if (this.colorUpdateTimer >= 1) {
      this.colorUpdateTimer = 0;
      this.pointer.color = this.generateColor();
    }
  }

  private applyInputs() {
    if (this.pointer.moved) {
      this.splat(
        this.pointer.texcoordX,
        this.pointer.texcoordY,
        this.pointer.deltaX,
        this.pointer.deltaY,
        this.pointer.color
      );
      this.pointer.moved = false;
    }
  }

  private step(_dt: number) {
    // Simple fluid simulation step
    // In a full implementation, this would include advection, diffusion, pressure solving, etc.
  }

  private render() {
    if (!this.displayProgram || !this.texture) return;

    const gl = this.gl;

    // Render to screen
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
    gl.clear(gl.COLOR_BUFFER_BIT);

    // Use display program
    gl.useProgram(this.displayProgram);

    // Set up attributes
    const aPosition = gl.getAttribLocation(this.displayProgram, "aPosition");
    gl.enableVertexAttribArray(aPosition);
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);

    // Set uniforms
    const uTexture = gl.getUniformLocation(this.displayProgram, "uTexture");
    gl.uniform1i(uTexture, 0);

    // Bind texture
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, this.texture);

    // Draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  public destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // Clean up WebGL resources
    const gl = this.gl;
    if (this.program) gl.deleteProgram(this.program);
    if (this.displayProgram) gl.deleteProgram(this.displayProgram);
    if (this.quadBuffer) gl.deleteBuffer(this.quadBuffer);
    if (this.texture) gl.deleteTexture(this.texture);
    if (this.framebuffer) gl.deleteFramebuffer(this.framebuffer);
  }

  public updateConfig(newConfig: Partial<FluidConfig>) {
    this.config = { ...this.config, ...newConfig };
  }
}
