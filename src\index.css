@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes pan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}



/* Reset scroll behavior for all elements */
html {
  scroll-behavior: auto;
}

/* Стили для фиксированного навбара */
body {
  padding-top: env(safe-area-inset-top);
}

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
    /* Disable smooth scrolling by default */
    scroll-behavior: auto;
  }

  body {
    @apply text-gray-800 bg-gray-50 dark:text-gray-200 dark:bg-gray-900;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold text-gray-900 dark:text-gray-200 leading-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  p {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }

  /* Custom Button Styles for CallToAction */
  .btn-light {
    @apply bg-white text-primary-dark font-semibold py-3 px-6 rounded-lg transition duration-300 ease-in-out border-2 border-white hover:bg-gray-100 hover:text-primary-darker focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white dark:bg-gray-900 dark:border-gray-800 dark:text-gray-200 dark:hover:bg-gray-800;
  }

  .btn-outline-light {
    @apply bg-transparent text-white font-semibold py-3 px-6 rounded-lg transition duration-300 ease-in-out border-2 border-white hover:bg-white hover:text-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white dark:border-gray-200 dark:text-gray-200 dark:hover:bg-gray-200;
  }
}

@layer components {
  .btn {
    @apply px-6 py-3 rounded-md font-medium transition-all duration-300 inline-flex items-center justify-center;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg dark:bg-blue-700 dark:hover:bg-blue-800;
  }

  .btn-secondary {
    @apply bg-green-600 text-white hover:bg-green-700 shadow-md hover:shadow-lg dark:bg-green-700 dark:hover:bg-green-800;
  }

  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-400 dark:hover:bg-blue-900;
  }

  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }

  .section {
    @apply py-12 md:py-20;
  }

  .card {
    @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden dark:bg-gray-800 dark:border-gray-700;
  }

  .form-input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:focus:ring-blue-400 dark:focus:border-blue-400;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1 dark:text-gray-200;
  }

  .form-group {
    @apply mb-4;
  }
}
