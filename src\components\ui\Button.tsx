import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

interface ButtonProps {
  children: React.ReactNode;
  to?: string;
  href?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  isExternal?: boolean;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  to,
  href,
  onClick,
  variant = 'primary',
  size = 'md',
  className = '',
  icon,
  iconPosition = 'right',
  isExternal = false,
  disabled = false,
}) => {
  // Базовые классы
  const baseClasses = 'btn-modern inline-flex items-center justify-center gap-2 font-medium transition-all rounded-lg';
  
  // Классы для вариантов
  const variantClasses = {
    primary: 'btn-primary-modern',
    secondary: 'btn-secondary-modern',
    outline: 'border-2 border-current hover:bg-opacity-10 hover:bg-white',
    ghost: 'hover:bg-opacity-10 hover:bg-white',
  };
  
  // Классы для размеров
  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3',
    lg: 'px-8 py-4 text-lg',
  };
  
  // Классы для состояния disabled
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';
  
  // Объединяем все классы
  const allClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;
  
  // Содержимое кнопки
  const content = (
    <>
      {icon && iconPosition === 'left' && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
      {icon && iconPosition === 'right' && <span className="flex-shrink-0">{icon}</span>}
    </>
  );
  
  // Рендер в зависимости от типа кнопки
  if (to) {
    return (
      <motion.div whileHover={{ scale: disabled ? 1 : 1.02 }} whileTap={{ scale: disabled ? 1 : 0.98 }}>
        <Link to={to} className={allClasses} onClick={disabled ? undefined : onClick}>
          {content}
        </Link>
      </motion.div>
    );
  }
  
  if (href) {
    return (
      <motion.div whileHover={{ scale: disabled ? 1 : 1.02 }} whileTap={{ scale: disabled ? 1 : 0.98 }}>
        <a 
          href={href} 
          className={allClasses} 
          onClick={disabled ? undefined : onClick}
          target={isExternal ? "_blank" : undefined}
          rel={isExternal ? "noopener noreferrer" : undefined}
        >
          {content}
        </a>
      </motion.div>
    );
  }
  
  return (
    <motion.button
      className={allClasses}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
    >
      {content}
    </motion.button>
  );
};

export default Button;