import React, { useCallback, useEffect, useRef } from "react";
import { FluidConfig, WebGLFluidSimulation } from "../../lib/webglFluidSimulation";

interface WebGLFluidBackgroundProps {
  className?: string;
  children?: React.ReactNode;
  config?: Partial<FluidConfig>;
  colors?: string[]; // Дополнительные цвета для эффекта
}

const WebGLFluidBackground: React.FC<WebGLFluidBackgroundProps> = ({
  className = "",
  children,
  config = {},
  colors = ["#3b82f6", "#8b5cf6", "#06b6d4", "#10b981", "#f59e0b", "#ef4444"],
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<WebGLFluidSimulation | null>(null);

  const isMobile = useCallback(() => {
    return /Mobi|Android/i.test(navigator.userAgent);
  }, []);

  // Конвертация hex цветов в RGB
  const hexToRgb = useCallback((hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (result && result[1] && result[2] && result[3]) {
      return [parseInt(result[1], 16) / 255, parseInt(result[2], 16) / 255, parseInt(result[3], 16) / 255];
    }
    return [0.5, 0.5, 1.0];
  }, []);

  // Настройки по умолчанию для красивого эффекта
  const defaultConfig: Partial<FluidConfig> = {
    SIM_RESOLUTION: isMobile() ? 64 : 128,
    DYE_RESOLUTION: isMobile() ? 512 : 1024,
    DENSITY_DISSIPATION: 1,
    VELOCITY_DISSIPATION: 0.2,
    PRESSURE: 0.8,
    PRESSURE_ITERATIONS: 20,
    CURL: 30,
    SPLAT_RADIUS: 0.25,
    SPLAT_FORCE: 6000,
    SHADING: true,
    COLORFUL: true,
    COLOR_UPDATE_SPEED: 10,
    PAUSED: false,
    BACK_COLOR: { r: 0, g: 0, b: 0 },
    TRANSPARENT: true,
    BLOOM: true,
    BLOOM_ITERATIONS: 8,
    BLOOM_RESOLUTION: 256,
    BLOOM_INTENSITY: 0.8,
    BLOOM_THRESHOLD: 0.6,
    BLOOM_SOFT_KNEE: 0.7,
    SUNRAYS: false,
    SUNRAYS_RESOLUTION: 196,
    SUNRAYS_WEIGHT: 1.0,
  };

  const finalConfig = { ...defaultConfig, ...config };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      // Создание WebGL Fluid Simulation
      simulationRef.current = new WebGLFluidSimulation(canvas, finalConfig);

      console.log("WebGL Fluid Simulation initialized successfully");
    } catch (error) {
      console.error("Failed to initialize WebGL Fluid Simulation:", error);
      // Fallback - можно показать простой эффект или сообщение
    }

    // Функция ресайза
    const resizeCanvas = () => {
      if (!containerRef.current || !canvas) return;

      const rect = containerRef.current.getBoundingClientRect();
      canvas.style.width = rect.width + "px";
      canvas.style.height = rect.height + "px";
    };

    // Инициальный ресайз
    resizeCanvas();

    // Обработчик ресайза окна
    const handleResize = () => {
      resizeCanvas();
    };

    window.addEventListener("resize", handleResize);

    // Автоматические сплаты для красоты
    const autoSplats = setInterval(() => {
      if (simulationRef.current && Math.random() < 0.3) {
        // Добавляем случайные сплаты для живости эффекта
        // В будущем здесь можно добавить автоматические сплаты
        console.log("Auto splat triggered");
      }
    }, 3000);

    return () => {
      window.removeEventListener("resize", handleResize);
      clearInterval(autoSplats);

      if (simulationRef.current) {
        simulationRef.current.destroy();
        simulationRef.current = null;
      }
    };
  }, [finalConfig, colors, hexToRgb]);

  return (
    <div ref={containerRef} className={`relative w-full h-full overflow-hidden ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{
          touchAction: "none",
          userSelect: "none",
        }}
      />
      {children && <div className="relative z-10 w-full h-full">{children}</div>}
    </div>
  );
};

export default WebGLFluidBackground;
